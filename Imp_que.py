#Q1 ) How to remove dublicate in string ?


"""

my_list = "aaaaaaaaaaaa"
dub_remove = "" .join(set(my_list))  #join are used for not show output for set form 
print(dub_remove)

using list: 

lst = [1, 2, 2, 3, 4, 4, 5]
unique_lst = list(set(lst))
print(unique_lst)  # Output: [1, 2, 3, 4, 5] (Order may change)

using tuple :

tup = (1, 2, 2, 3, 4, 4, 5)
unique_tup = tuple(set(tup))
print(unique_tup)  # Output: (1, 2, 3, 4, 5) (Order may change)
 


"""

# que2 : sapose in o/p file is <PERSON><PERSON><PERSON> is present so i want replace this name into <PERSON><PERSON> how you do

# with open ("file.txt", "r+") as file:
#     rep = file.read()
#     new_file = rep.replace("<PERSON>iket","<PERSON>ita")
#     file.write(new_file)
#     file.truncate()
# print(new_file)

"""
with open("file.txt", "r+") as file:
    rep = file.read()  # ✅ संपूर्ण फाईल वाचा
    new_file = rep.replace("<PERSON><PERSON>", "<PERSON>ike<PERSON>")  # ✅ "Aniket" हा शब्द "Nikita" ने बदला
    
    file.seek(0)  # ✅ फाईलच्या सुरुवातीला जा
    file.write(new_file)  # ✅ अपडेटेड मजकूर लिहा
    file.truncate()  # ✅ उर्वरित जुन्या मजकुराला काढून टाका (जर नवीन मजकूर कमी असेल तर)

print("Updated file content:\n", new_file)  # ✅ स्क्रीनवर अपडेटेड मजकूर प्रिंट करा

"""
"""
with open ("file.txt","r+") as file:
    read_file = file.read()
    replace_word = read_file.replace("Aniket","Nikita")
    file.seek(0)
    file.write(replace_word)
    file.truncate()

print(replace_word)

"""
#uisng a for loop

"""
with open ("file.txt","r+") as file:
    lines = file.readlines()

    new_lines = []
    for line in lines:
        new_lines.append(line.replace("Aniket","Ani"))
    file.seek(0)
    file.writelines(new_lines)
    file.truncate()

print(new_lines)

"""

#How do you unpack a tuple into variables?

"""
Exmaple_tup = ["A","B","C"]
a,b,c  = Exmaple_tup
print(a,b,c)

"""
# string reverse ?

"""
a = 'iasys'
print(a[::-1])

"""
