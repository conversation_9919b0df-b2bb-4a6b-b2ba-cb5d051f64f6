"""
list : it is mutable data type 
it mens you can allow to modfied data , 
it is solw for as compaire to tuple 
it is indicate for [] squre braket

methods : revers , count , sort, append , inssert , pop , remove

"""
l1 = ["<PERSON><PERSON><PERSON>" , "nikita", "<PERSON><PERSON>", "mummy", "papa"]
print(type(l1))

l1.sort() #sort method to used for sort any list of asc order
print(l1)
l1.reverse() #revers method to used for reverse list in revers order 
print(l1)
l1.pop(2) # it is used for remove list supply word position 
print(l1)
l1.remove("Aniket") #it is remove pass words form list
print(l1)
l1.append("<PERSON><PERSON>") # append data from end of the list
print(l1)
l1.insert(2 , "<PERSON><PERSON>") #insrt method to use for insrt into supply position 
print(l1)