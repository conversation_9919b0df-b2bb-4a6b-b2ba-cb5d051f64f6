"""
dictionary : it is unoodered , indexed 
 it is a key and value pairs 
 it is a mutable 
 it' s connot contain dublicate value 

 method : keys, update, items, get,remove , del

"""

#method 1 : key()

my_dic = {
            "<PERSON>ike<PERSON>" :  20,
            "nikita" :  21,
            "<PERSON><PERSON>" :   1,
            "mummy" :  50,
            "papa" : 60
}

print(my_dic.keys()) # key method get only key of the dic
print(my_dic.items()) #item method is get key and value pairs of the dic
my_dic.update({"Ankit ": 2})
print(my_dic)  #update method you have to add key value pairs in existing dic
print(my_dic.get("Aniket")) #it is a get supply value in the dict
del my_dic["Aniket"] #del is used for the delete existing dict key otherwise value
print(my_dic)
my_dic["Aniket"] = 30
print(my_dic) # second method to update existing dict