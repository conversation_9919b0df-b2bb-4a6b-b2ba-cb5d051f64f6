"""
string data type 

methods : len , endswith, concat , count, replace, find , capitalzed

"""
'''

#method 1 len : it is find the length of the string

str_name = "<PERSON>ike<PERSON>"
print(type(str_name))
print(len(str_name))

#method 2 endswith : it is replace true if it passed end character is mathched other wise flase 

str_name = "<PERSON>ike<PERSON>"
e_name = str_name.endswith("et")
print(e_name)

#method 3 : concatnation : it is concat two string

frist_str = "Aniket"
sec_str = "Sarate"
print(frist_str + " " + sec_str)

#method 4 = count :  it is show supply letter count

str_name = "Anike<PERSON>"
cnt = str_name.count("t")
print(cnt)

#method 5 : find : it is find the position of the supply string letters

f_str = "Aniket" 
sf_str = f_str.find("n")
print(sf_str)

#method 6 : capitalazation  : it is give o/p of pass string letters frist capital

cap_str = "aniket"
pcap_str = cap_str.capitalize()
print(pcap_str)

#method 7 : replace : it is replace old value into new value

r_str = "<PERSON>ike<PERSON>"
re_str = r_str.replace("<PERSON>ike<PERSON>","<PERSON><PERSON>")
print(re_str)
'
'''
"""
Python String Data Type - FAQ
"""

# 1. What is a string in Python?
# A string is a sequence of characters enclosed in quotes.

# 2. Declaring strings
str1 = 'Hello'  # Single quotes
str2 = "Hello"  # Double quotes
str3 = '''Hello'''  # Triple quotes
str4 = """Hello"""  # Triple double quotes

# 3. Are strings mutable?
# No, strings are immutable in Python.

# 4. Accessing characters
s = "Python"
print(s[0])   # 'P'
print(s[-1])  # 'n'
print(s[1:4]) # 'yth'

# 5. Reversing a string
reversed_s = s[::-1]
print(reversed_s)  # 'nohtyP'

# 6. Concatenating strings
s1 = "Hello"
s2 = "World"
print(s1 + " " + s2)  # 'Hello World'
print(" ".join([s1, s2]))  # 'Hello World'

# 7. Repeating a string
s = "Hi "
print(s * 3)  # 'Hi Hi Hi '

# 8. Common string methods
s = " Hello World "
print(s.lower())  # ' hello world '
print(s.upper())  # ' HELLO WORLD '
print(s.strip())  # 'Hello World'
print(s.replace("World", "Python"))  # ' Hello Python '
print(s.split())  # ['Hello', 'World']

# 9. Checking substring existence
print("Python" in s)  # True
print("Java" in s)    # False

# 10. Formatting strings
name = "Alice"
age = 25
print(f"My name is {name} and I am {age} years old.")
print("My name is {} and I am {} years old.".format(name, age))
print("My name is %s and I am %d years old." % (name, age))

# 11. Checking if a string is numeric
print("12345".isdigit())  # True
print("12.34".isdigit())  # False

# 12. Checking start and end of string
s = "hello world"
print(s.startswith("hello"))  # True
print(s.endswith("world"))  # True

# 13. String character checks
print("hello".isalpha())  # True
print("123".isdigit())  # True
print("hello123".isalnum())  # True

# 14. Removing whitespace
s = "  hello  "
print(s.strip())  # 'hello'
print(s.lstrip())  # 'hello  '
print(s.rstrip())  # '  hello'

# 15. Converting string to list
s = "hello"
print(list(s))  # ['h', 'e', 'l', 'l', 'o']

# 16. Splitting and partitioning
s = "apple,banana,grape"
print(s.split(","))  # ['apple', 'banana', 'grape']
print(s.partition(","))  # ('apple', ',', 'banana,grape')

# 17. Converting string to bytes and vice versa
s = "Hello"
b = s.encode("utf-8")
print(b)  # b'Hello'
s2 = b.decode("utf-8")
print(s2)  # 'Hello'

# 18. Escaping special characters
print("He said \"Hello\"")  # He said "Hello"
print('It\'s a good day')  # It's a good day

# 19. Removing specific characters
s = "Hello! Welcome!!"
print(s.replace("!", ""))  # 'Hello Welcome'

# 20. Checking for anagrams
s1 = "listen"
s2 = "silent"
print(sorted(s1) == sorted(s2))  # True


