"""
for loop 
it is working to (start,stop,step) condition

"""
#Right-Angled Triangle * pattern 
"""
n= 5
for i in range (1,n+1):
    for j in range(1,i+1):
        print("*",end=" ")
    print()

#Right-Angled Triangle number pattern 

n= 5

for i in range(1,n+1):
    for j in range(1,i+1):
     print(j , end=" ")
    print()
"""


#Inverted Right-Angled Triangle * pattern

"""
n= 5
for i in range(n,0,-1):
    for j in range(i):
        print("*",end = " ")
    print()

#Inverted Right-Angled Triangle number pattern

n= 5
for i in range (n,0,-1):
    for j in range(1,i+1):
     print("*", end=" ")
    print()

"""

#Pyramid

"""
n= 5
for i in range(1,n+1):
    print(" " * (n-i) + "* " * i)

"""

#Inverted Pyramid
"""
n=5
for i in range(n,0,-1):
    print(" " * (n-i) + "* " * i)

"""

#Diamond Pattern

"""
n =5
for i in range (1,n+1,2):
    print(" " * (n-i//2)+ "*" *i)
for i in range(n+2,0,-2):
    print(" " * (n-i//2) + "*" *i)


"""

#Alternate 0-1 Pattern
"""
numbr_per = 5

for i in range (1,numbr_per+1,1):
    for j in range(1,i+1):
        print((i+j) %2 , end= " ")
    print()

"""
#Fibonacci Pyramid

"""
n= 5
a,b = 0, 1

for i in range (1,n+1,1):
    for j in range (1,i+1):
        print(a, end = " ")
        a,b = b , a+b
    print()

"""
