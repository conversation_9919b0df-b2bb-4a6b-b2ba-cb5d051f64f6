"""
Numbric data type:

1) integer
2) float
3) complex

"""
'''
int_datatype = 3
print(type(int_datatype))

flo_datatype = 1.1
print(type(flo_datatype))

com_datatype = 2+2j
print(type(com_datatype))'
'
'''

""""
o/p : 

<class 'int'>
<class 'float'>
<class 'complex'>
"""

#numbric data type

# ther are three numbric data type in python float , integer , complx 

inte = 23
print(type(inte))

folt = 23.5
print(type(folt))

cmpx  = 2+5j
print(type(cmpx))