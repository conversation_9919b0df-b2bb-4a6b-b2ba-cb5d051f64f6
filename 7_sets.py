"""
sets : it is collecion of non-repetitve element
it is unorderd and unindexed 
there is no way to change items in sets
sets can not contains dublicated value
method : len,remove,pop,clear , add , update, union | , intersection & , symetric_difference ^ , difference (-) 

"""

my_set = {1,5,4,8,6,3,5,4,4,4,}
my_set2 = {1,5,4,8,6,3,59,4,8,4,}
print(len(my_set)) # it is show len of the set

my_set.remove(5)
print(my_set) # remove method is used to remove supply value 

my_set.clear()
print(my_set) #clear the set 

my_set.update([23])
print(my_set) #update vlalue in a existing set

my_set.add(28)
print(my_set) # it is same to a update

unni_myset = my_set | my_set2
print(unni_myset) # it is remove dublicate and show all value for both sets

intt_myset = my_set & my_set2
print(f"my intersection output is {intt_myset }") # it is remove dublicate and show unique value for both sets

#sys_diff = it is same for unino

diif = my_set - my_set2
print(diif)   # it is difference method it is  remove dublicate from my_set with my_set show all value my_set.

diff = my_set2 - my_set
print(diif) # it is difference method it is  remove dublicate from my_set2 with my_set show all value my_set2.

