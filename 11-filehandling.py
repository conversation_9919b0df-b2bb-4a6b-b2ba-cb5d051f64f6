"""
file handling we have to using read, write , append , exclusive creation mode , read write method , write read method .


"""

# write method : it is create new file and store the passed setence and alredy file created the trucate the existing and
#add new passed sentence 

"""
with open ("file.txt", "w") as file:
    re_met = file.write("Aniket is good boy")
    print(re_met)
    
"""

#read method : it is used for the read passed file sentence 

"""
with open ("file.txt") as file:
    content = file.read()
    print(content)

"""
#append method : it is a append the passed sentence in existing file last of file sentence 

"""
with open ("file.txt", "a") as file:
    content = file.write(" \n but its bad boy" )
    print(content)
"""
#readline  : it is show only one line & readlines : it is showing all data for a list format

"""
with open ("file.txt","r") as file :
    print(file.readlines())

"""

# read write : read and write the not a overwrite the file 
"""
with open ("file.txt","r+") as file:
    print(file.read())
    file.seek(0) #using the pointer of the file which location data we placed 
    print(file.write("this the file"))

"""
#write and read : old file is present then this file data is truncted then file is not present then new file is created 

"""
with open ("file.txt","w+") as file:
    print(file.write("Aniket is good boy"))
    file.seek(0)
    print(file.read())

 """   
# json file read and write method

#write 
"""
import json

data = {"name":"aniket"}
with open("data.json","w") as file:
    json.dump(data,file, indent=4)
    
"""

#read

"""
with open ("data.json","r") as file:
    print(file.read())
    
"""
# csv file read and write method
 
# write method 
"""   
import csv 

with open ("data.csv", "w", newline="") as file:
    write = csv.writer(file)
    write.writerow(["Name","Age"])
"""

# read method 

"""
with open ("data.csv","r") as file:
    reder = (file.read())
    for row in reder:
        print(row)

"""