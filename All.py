
# '''
# my_name  = "<PERSON><PERSON><PERSON>"
# my_sname = "sarate"
# print(my_name, end = " ") #method1
# print(my_sname)
# print(my_name,my_sname) #method 2

# #concatnation two string

# print(my_name + " " + my_sname) #method 3

# '''

# my_name = "<PERSON>ike<PERSON>"
# my_sname = "sarate"

# print(my_name, end = " ")
# print(my_sname)



# """
# Numbric data type:

# 1) integer 
# 2) float
# 3) complex

# """
# '''
# int_datatype = 3
# print(type(int_datatype))

# flo_datatype = 1.1
# print(type(flo_datatype))

# com_datatype = 2+2j
# print(type(com_datatype))'
# '
# '''

# """"
# o/p : 

# <class 'int'>
# <class 'float'>
# <class 'complex'>
# """

# #numbric data type

# # ther are three numbric data type in python float , integer , complx 

# inte = 23
# print(type(inte))

# folt = 23.5
# print(type(folt))

# cmpx  = 2+5j
# print(type(cmpx))

a = 1
b = 1.1
c= 2+3j

print(type(a))
print(type(b))
print(type(c))



# """
# string data type 

# methods : len , endswith, concat , count, replace, find , capitalzed

# # 4. Accessing characters
# s = "Python"
# print(s[0])   # 'P'
# print(s[-1])  # 'n'
# print(s[1:4]) # 'yth'

# """
# '''
# #method 1 len : it is find the length of the string

# str_name = "Aniket"
# print(type(str_name))
# print(len(str_name))

# #method 2 endswith : it is replace true if it passed end character is mathched other wise flase 

# str_name = "Aniket"
# e_name = str_name.endswith("et")
# print(e_name)

# #method 3 : concatnation : it is concat two string

# frist_str = "Aniket"
# sec_str = "Sarate"
# print(frist_str + " " + sec_str)

# #method 4 = count :  it is show supply letter count

# str_name = "Anikett"
# cnt = str_name.count("t")
# print(cnt)

# #method 5 : find : it is find the position of the supply string letters

# f_str = "Aniket" 
# sf_str = f_str.find("n")
# print(sf_str)

# #method 6 : capitalazation  : it is give o/p of pass string letters frist capital

# cap_str = "aniket"
# pcap_str = cap_str.capitalize()
# print(pcap_str)

# #method 7 : replace : it is replace old value into new value

# r_str = "Aniket"
# re_str = r_str.replace("Aniket","Nikita")
# print(re_str)
# '
# '''
# """
# Python String Data Type - FAQ
# """

# # 1. What is a string in Python?
# # A string is a sequence of characters enclosed in quotes.

# # 2. Declaring strings
# str1 = 'Hello'  # Single quotes
# str2 = "Hello"  # Double quotes
# str3 = '''Hello'''  # Triple quotes
# str4 = """Hello"""  # Triple double quotes

# # 3. Are strings mutable?
# # No, strings are immutable in Python.

# # 4. Accessing characters
# s = "Python"
# print(s[0])   # 'P'
# print(s[-1])  # 'n'
# print(s[1:4]) # 'yth'

# # 5. Reversing a string
# reversed_s = s[::-1]
# print(reversed_s)  # 'nohtyP'

# # 6. Concatenating strings
# s1 = "Hello"
# s2 = "World"
# print(s1 + " " + s2)  # 'Hello World'
# print(" ".join([s1, s2]))  # 'Hello World'

# # 7. Repeating a string
# s = "Hi "
# print(s * 3)  # 'Hi Hi Hi '

# # 8. Common string methods
# s = " Hello World "
# print(s.lower())  # ' hello world '
# print(s.upper())  # ' HELLO WORLD '
# print(s.strip())  # 'Hello World'
# print(s.replace("World", "Python"))  # ' Hello Python '
# print(s.split())  # ['Hello', 'World']

# # 9. Checking substring existence
# print("Python" in s)  # True
# print("Java" in s)    # False

# # 10. Formatting strings
# name = "Alice"
# age = 25
# print(f"My name is {name} and I am {age} years old.")
# print("My name is {} and I am {} years old.".format(name, age))
# print("My name is %s and I am %d years old." % (name, age))

# # 11. Checking if a string is numeric
# print("12345".isdigit())  # True
# print("12.34".isdigit())  # False

# # 12. Checking start and end of string
# s = "hello world"
# print(s.startswith("hello"))  # True
# print(s.endswith("world"))  # True

# # 13. String character checks
# print("hello".isalpha())  # True
# print("123".isdigit())  # True
# print("hello123".isalnum())  # True

# # 14. Removing whitespace
# s = "  hello  "
# print(s.strip())  # 'hello'
# print(s.lstrip())  # 'hello  '
# print(s.rstrip())  # '  hello'

# # 15. Converting string to list
# s = "hello"
# print(list(s))  # ['h', 'e', 'l', 'l', 'o']

# # 16. Splitting and partitioning
# s = "apple,banana,grape"
# print(s.split(","))  # ['apple', 'banana', 'grape']
# print(s.partition(","))  # ('apple', ',', 'banana,grape')

# # 17. Converting string to bytes and vice versa
# s = "Hello"
# b = s.encode("utf-8")
# print(b)  # b'Hello'
# s2 = b.decode("utf-8")
# print(s2)  # 'Hello'

# # 18. Escaping special characters
# print("He said \"Hello\"")  # He said "Hello"
# print('It\'s a good day')  # It's a good day

# # 19. Removing specific characters
# s = "Hello! Welcome!!"
# print(s.replace("!", ""))  # 'Hello Welcome'

# # 20. Checking for anagrams
# s1 = "listen"
# s2 = "silent"
# print(sorted(s1) == sorted(s2))  # True



# """
# list : it is mutable data type 
# it mens you can allow to modfied data , 
# it is solw for as compaire to tuple 
# it is indicate for [] squre braket

# methods : revers , count , sort, append , inssert , pop , remove

# """
# l1 = ["Aniket" , "nikita", "Anika", "mummy", "papa"]
# print(type(l1))

# l1.sort() #sort method to used for sort any list of asc order
# print(l1)
# l1.reverse() #revers method to used for reverse list in revers order 
# print(l1)
# l1.pop(2) # it is used for remove list supply word position 
# print(l1)
# l1.remove("Aniket") #it is remove pass words form list
# print(l1)
# l1.append("Sarate") # append data from end of the list
# print(l1)
# l1.insert(2 , "Nikita") #insrt method to use for insrt into supply position 
# print(l1)


# """
# tuple : it is a immutable data type 

# it means you can not be add remove.

# it is faster with comapir to a list

# it is denoted by a open close braket

# method : count , index

# """
# tup = (1,2,3,5,8,9)
# print(tup.count(1)) # it is show o/p of the pass word letter co
# print(tup.index(3)) # it is show pass word unt in hole tupleletter position


# """
# dictionary : it is unoodered , indexed 
#  it is a key and value pairs 
#  it is a mutable 
#  it' s connot contain dublicate value 

#  method : keys, update, items, get,remove , del

# """

# #method 1 : key()

# my_dic = {
#             "Aniket" :  20,
#             "nikita" :  21,
#             "Anika" :   1,
#             "mummy" :  50,
#             "papa" : 60
# }

# print(my_dic.keys()) # key method get only key of the dic
# print(my_dic.items()) #item method is get key and value pairs of the dic
# my_dic.update({"Ankit ": 2})
# print(my_dic)  #update method you have to add key value pairs in existing dic
# print(my_dic.get("Aniket")) #it is a get supply value in the dict
# del my_dic["Aniket"] #del is used for the delete existing dict key otherwise value
# print(my_dic)
# my_dic["Aniket"] = 30
# print(my_dic) # second method to update existing dict


# """
# sets : it is collecion of non-repetitve element
# it is unorderd and unindexed 
# there is no way to change items in sets
# sets can not contains dublicated value
# method : len,remove,pop,clear , add , update, union | , intersection & , symetric_difference ^ , difference (-) 

# """

# my_set = {1,5,4,8,6,3,5,4,4,4,}
# my_set2 = {1,5,4,8,6,3,59,4,8,4,}
# print(len(my_set)) # it is show len of the set

# my_set.remove(5)
# print(my_set) # remove method is used to remove supply value 

# my_set.clear()
# print(my_set) #clear the set 

# my_set.update([23])
# print(my_set) #update vlalue in a existing set

# my_set.add(28)
# print(my_set) # it is same to a update

# unni_myset = my_set | my_set2
# print(unni_myset) # it is remove dublicate and show all value for both sets

# intt_myset = my_set & my_set2
# print(f"my intersection output is {intt_myset }") # it is remove dublicate and show unique value for both sets

# #sys_diff = it is same for unino

# diif = my_set - my_set2
# print(diif)   # it is difference method it is  remove dublicate from my_set with my_set show all value my_set.

# diff = my_set2 - my_set
# print(diif) # it is difference method it is  remove dublicate from my_set2 with my_set show all value my_set2.



# """
# if , elif ,else
# """

# user = int(input("Enter your age : "))

# if user >=18:
#     print("You are egligible")
# elif user <= 18 and user >0 :
#     print("Your are not egligible")
# elif user <= 0 :
#     print("you are enter wrong value") 
# else :
#     print("please reloging and check")


# """
# while loop
# """
# # x= 0
# # while x<5:
# #     print("Aniket")
# #     x+=1

# user = int(input("please enter your age : "))

# while user >=18 :
#     print("Your are egligibale")
#     break

# else:
#     print("you are not egligible")

# """
# for loop 
# it is working to (start,stop,step) condition

# """
# #Right-Angled Triangle * pattern 
# """
# n= 5
# for i in range (1,n+1):
#     for j in range(1,i+1):
#         print("*",end=" ")
#     print()

# #Right-Angled Triangle number pattern 

# n= 5

# for i in range(1,n+1):
#     for j in range(1,i+1):
#      print(j , end=" ")
#     print()
# """


# #Inverted Right-Angled Triangle * pattern

# """
# n= 5
# for i in range(n,0,-1):
#     for j in range(i):
#         print("*",end = " ")
#     print()

# #Inverted Right-Angled Triangle number pattern

# n= 5
# for i in range (n,0,-1):
#     for j in range(1,i+1):
#      print("*", end=" ")
#     print()

# """

# #Pyramid

# """
# n= 5
# for i in range(1,n+1):
#     print(" " * (n-i) + "* " * i)

# """

# #Inverted Pyramid
# """
# n=5
# for i in range(n,0,-1):
#     print(" " * (n-i) + "* " * i)

# """

# #Diamond Pattern

# """
# n =5
# for i in range (1,n+1,2):
#     print(" " * (n-i//2)+ "*" *i)
# for i in range(n+2,0,-2):
#     print(" " * (n-i//2) + "*" *i)


# """

# #Alternate 0-1 Pattern
# """
# numbr_per = 5

# for i in range (1,numbr_per+1,1):
#     for j in range(1,i+1):
#         print((i+j) %2 , end= " ")
#     print()

# """
# #Fibonacci Pyramid

# """
# n= 5
# a,b = 0, 1

# for i in range (1,n+1,1):
#     for j in range (1,i+1):
#         print(a, end = " ")
#         a,b = b , a+b
#     print()

# """


# """
# file handling we have to using read, write , append , exclusive creation mode , read write method , write read method .


# """

# # write method : it is create new file and store the passed setence and alredy file created the trucate the existing and
# #add new passed sentence 

# """
# with open ("file.txt", "w") as file:
#     re_met = file.write("Aniket is good boy")
#     print(re_met)
    
# """

# #read method : it is used for the read passed file sentence 

# """
# with open ("file.txt") as file:
#     content = file.read()
#     print(content)

# """
# #append method : it is a append the passed sentence in existing file last of file sentence 

# """
# with open ("file.txt", "a") as file:
#     content = file.write(" \n but its bad boy" )
#     print(content)
# """
# #readline  : it is show only one line & readlines : it is showing all data for a list format

# """
# with open ("file.txt","r") as file :
#     print(file.readlines())

# """

# # read write : read and write the not a overwrite the file 
# """
# with open ("file.txt","r+") as file:
#     print(file.read())
#     file.seek(0) #using the pointer of the file which location data we placed 
#     print(file.write("this the file"))

# """
# #write and read : old file is present then this file data is truncted then file is not present then new file is created 

# """
# with open ("file.txt","w+") as file:
#     print(file.write("Aniket is good boy"))
#     file.seek(0)
#     print(file.read())

#  """   
# # json file read and write method

# #write 
# """
# import json

# data = {"name":"aniket"}
# with open("data.json","w") as file:
#     json.dump(data,file, indent=4)
    
# """

# #read

# """
# with open ("data.json","r") as file:
#     print(file.read())
    
# """
# # csv file read and write method
 
# # write method 
# """   
# import csv 

# with open ("data.csv", "w", newline="") as file:
#     write = csv.writer(file)
#     write.writerow(["Name","Age"])
# """

# # read method 

# """
# with open ("data.csv","r") as file:
#     reder = (file.read())
#     for row in reder:
#         print(row)

# """

# """
# Python functions are reusable blocks of code that perform a specific task

# """
    
# def testing (name, age = 26):
#     print(f"type name is:{name} and age {age}")
# testing("Aniket")


# #Q1 ) How to remove dublicate in string ?


# """

# my_list = "aaaaaaaaaaaa"
# dub_remove = "" .join(set(my_list))  #join are used for not show output for set form 
# print(dub_remove)

# using list: 

# lst = [1, 2, 2, 3, 4, 4, 5]
# unique_lst = list(set(lst))
# print(unique_lst)  # Output: [1, 2, 3, 4, 5] (Order may change)

# using tuple :

# tup = (1, 2, 2, 3, 4, 4, 5)
# unique_tup = tuple(set(tup))
# print(unique_tup)  # Output: (1, 2, 3, 4, 5) (Order may change)
 


# """

# # que2 : sapose in o/p file is Aniket is present so i want replace this name into Nikita how you do

# # with open ("file.txt", "r+") as file:
# #     rep = file.read()
# #     new_file = rep.replace("Aniket","Nikita")
# #     file.write(new_file)
# #     file.truncate()
# # print(new_file)

# """
# with open("file.txt", "r+") as file:
#     rep = file.read()  # ✅ संपूर्ण फाईल वाचा
#     new_file = rep.replace("Nikita", "Aniket")  # ✅ "Aniket" हा शब्द "Nikita" ने बदला
    
#     file.seek(0)  # ✅ फाईलच्या सुरुवातीला जा
#     file.write(new_file)  # ✅ अपडेटेड मजकूर लिहा
#     file.truncate()  # ✅ उर्वरित जुन्या मजकुराला काढून टाका (जर नवीन मजकूर कमी असेल तर)

# print("Updated file content:\n", new_file)  # ✅ स्क्रीनवर अपडेटेड मजकूर प्रिंट करा

# """
# """
# with open ("file.txt","r+") as file:
#     read_file = file.read()
#     replace_word = read_file.replace("Aniket","Nikita")
#     file.seek(0)
#     file.write(replace_word)
#     file.truncate()

# print(replace_word)

# """
# #uisng a for loop

# """
# with open ("file.txt","r+") as file:
#     lines = file.readlines()

#     new_lines = []
#     for line in lines:
#         new_lines.append(line.replace("Aniket","Ani"))
#     file.seek(0)
#     file.writelines(new_lines)
#     file.truncate()

# print(new_lines)

# """

# #How do you unpack a tuple into variables?

# """
# Exmaple_tup = ["A","B","C"]
# a,b,c  = Exmaple_tup
# print(a,b,c)

# """
# # string reverse ?

# """
# a = 'iasys'
# print(a[::-1])

# """
